<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FeedbackResponse extends Model
{
    use HasFactory;

    protected $fillable = [
        'feedback_id',
        'user_id',
        'response',
        'type',
        'is_visible_to_submitter',
        'metadata',
    ];

    protected $casts = [
        'is_visible_to_submitter' => 'boolean',
        'metadata' => 'array',
    ];

    // Relationships
    public function feedback(): BelongsTo
    {
        return $this->belongsTo(Feedback::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopePublic($query)
    {
        return $query->where('is_visible_to_submitter', true);
    }

    public function scopeInternal($query)
    {
        return $query->where('is_visible_to_submitter', false);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    // Helper methods
    public function isPublicResponse(): bool
    {
        return $this->type === 'public_response' && $this->is_visible_to_submitter;
    }

    public function isInternalNote(): bool
    {
        return $this->type === 'internal_note' || !$this->is_visible_to_submitter;
    }
}
