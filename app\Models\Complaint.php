<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Complaint extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'subject',
        'description',
        'status',
        'feedback_id',
        'migrated_to_feedback',
    ];

    protected $casts = [
        'migrated_to_feedback' => 'boolean',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function feedback(): BelongsTo
    {
        return $this->belongsTo(Feedback::class);
    }

    // Helper methods
    public function migrateToFeedback(): Feedback
    {
        if ($this->migrated_to_feedback && $this->feedback) {
            return $this->feedback;
        }

        $feedback = Feedback::create([
            'type' => 'complaint',
            'title' => $this->subject,
            'description' => $this->description,
            'user_id' => $this->user_id,
            'feedback_category_id' => FeedbackCategory::where('slug', 'other')->first()?->id ?? 1,
            'feedback_status_id' => FeedbackStatus::getInitialStatus()?->id ?? 1,
            'priority' => 'medium',
            'urgency' => 'medium',
        ]);

        $this->update([
            'feedback_id' => $feedback->id,
            'migrated_to_feedback' => true,
        ]);

        return $feedback;
    }
}
