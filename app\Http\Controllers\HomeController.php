<?php

namespace App\Http\Controllers;

use App\Models\Feedback;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HomeController extends Controller
{
    // Home page with login form
    public function index()
    {
        if (Auth::check()) {
            return $this->dashboard();
        }
        return view('home');
    }

    // Login
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $user = User::where('email', $request->email)->first();

        if ($user && $user->password === $request->password) {
            Auth::login($user);
            $request->session()->regenerate();
            return redirect()->route('home');
        }

        return back()->withErrors([
            'email' => 'Invalid credentials.',
        ]);
    }

    // Logout
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('home');
    }

    // Dashboard based on user role
    public function dashboard()
    {
        $user = Auth::user();
        
        if ($user->isAdmin()) {
            return $this->adminDashboard();
        } elseif ($user->isTeacher()) {
            return $this->teacherDashboard();
        } else {
            return $this->studentDashboard();
        }
    }

    // Admin Dashboard
    private function adminDashboard()
    {
        $feedback = Feedback::with('user')->latest()->get();
        $stats = [
            'total' => Feedback::count(),
            'pending' => Feedback::where('status', 'pending')->count(),
            'in_progress' => Feedback::where('status', 'in_progress')->count(),
            'resolved' => Feedback::where('status', 'resolved')->count(),
        ];
        
        return view('admin.dashboard', compact('feedback', 'stats'));
    }

    // Teacher Dashboard
    private function teacherDashboard()
    {
        $feedback = Feedback::with('user')->latest()->get();
        return view('teacher.dashboard', compact('feedback'));
    }

    // Student Dashboard
    private function studentDashboard()
    {
        $feedback = Auth::user()->feedback()->latest()->get();
        return view('student.dashboard', compact('feedback'));
    }

    // Submit feedback form
    public function submitForm()
    {
        return view('feedback.submit');
    }

    // Store feedback
    public function storeFeedback(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
        ]);

        $feedback = Feedback::create([
            'title' => $request->title,
            'description' => $request->description,
            'user_id' => Auth::id(),
        ]);

        return redirect()->route('feedback.track', $feedback->tracking_number)
            ->with('success', 'Feedback submitted successfully! Tracking number: ' . $feedback->tracking_number);
    }

    // Track feedback
    public function trackFeedback($trackingNumber = null)
    {
        if (!$trackingNumber) {
            return view('feedback.track');
        }

        $feedback = Feedback::where('tracking_number', $trackingNumber)->with('user')->first();
        
        if (!$feedback) {
            return redirect()->route('feedback.track')->withErrors(['error' => 'Feedback not found.']);
        }

        return view('feedback.show', compact('feedback'));
    }

    // Track feedback POST
    public function trackFeedbackPost(Request $request)
    {
        $request->validate([
            'tracking_number' => 'required|string',
        ]);

        return redirect()->route('feedback.track', $request->tracking_number);
    }

    // Update feedback status (admin only)
    public function updateStatus(Request $request, Feedback $feedback)
    {
        if (!Auth::user()->isAdmin()) {
            abort(403);
        }

        $request->validate([
            'status' => 'required|in:pending,in_progress,resolved,closed',
        ]);

        $feedback->update(['status' => $request->status]);

        return back()->with('success', 'Status updated successfully.');
    }
}
