<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FeedbackStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statuses = [
            [
                'name' => 'Submitted',
                'slug' => 'submitted',
                'description' => 'Feedback has been submitted and is awaiting initial review',
                'color' => '#6B7280',
                'is_initial' => true,
                'is_final' => false,
                'sort_order' => 1,
            ],
            [
                'name' => 'Under Review',
                'slug' => 'under-review',
                'description' => 'Feedback is being reviewed by the appropriate department',
                'color' => '#3B82F6',
                'is_initial' => false,
                'is_final' => false,
                'sort_order' => 2,
            ],
            [
                'name' => 'In Progress',
                'slug' => 'in-progress',
                'description' => 'Action is being taken to address the feedback',
                'color' => '#F59E0B',
                'is_initial' => false,
                'is_final' => false,
                'sort_order' => 3,
            ],
            [
                'name' => 'Pending Information',
                'slug' => 'pending-information',
                'description' => 'Waiting for additional information from the submitter',
                'color' => '#8B5CF6',
                'is_initial' => false,
                'is_final' => false,
                'sort_order' => 4,
            ],
            [
                'name' => 'Escalated',
                'slug' => 'escalated',
                'description' => 'Feedback has been escalated to higher management',
                'color' => '#EF4444',
                'is_initial' => false,
                'is_final' => false,
                'sort_order' => 5,
            ],
            [
                'name' => 'Resolved',
                'slug' => 'resolved',
                'description' => 'Feedback has been addressed and resolved',
                'color' => '#10B981',
                'is_initial' => false,
                'is_final' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'Closed',
                'slug' => 'closed',
                'description' => 'Feedback is closed and no further action is required',
                'color' => '#6B7280',
                'is_initial' => false,
                'is_final' => true,
                'sort_order' => 7,
            ],
            [
                'name' => 'Rejected',
                'slug' => 'rejected',
                'description' => 'Feedback was reviewed but rejected with explanation',
                'color' => '#DC2626',
                'is_initial' => false,
                'is_final' => true,
                'sort_order' => 8,
            ],
            [
                'name' => 'Duplicate',
                'slug' => 'duplicate',
                'description' => 'This feedback is a duplicate of an existing submission',
                'color' => '#9CA3AF',
                'is_initial' => false,
                'is_final' => true,
                'sort_order' => 9,
            ],
        ];

        foreach ($statuses as $status) {
            DB::table('feedback_statuses')->insert([
                ...$status,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
