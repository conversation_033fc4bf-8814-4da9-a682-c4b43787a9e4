<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
public function up(): void
{
    Schema::create('feedback', function (Blueprint $table) {
        $table->id();
        $table->string('tracking_number')->unique(); // For public tracking
        $table->enum('type', ['feedback', 'suggestion', 'complaint'])->default('feedback');
        $table->string('title');
        $table->text('description');

        // Submitter information
        $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Authenticated user
        $table->string('anonymous_name')->nullable(); // For anonymous submissions
        $table->string('anonymous_email')->nullable(); // For anonymous submissions
        $table->boolean('is_anonymous')->default(false);

        // Categorization
        $table->foreignId('feedback_category_id')->constrained()->onDelete('cascade');
        $table->foreignId('feedback_status_id')->constrained()->onDelete('cascade');

        // Priority and urgency
        $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
        $table->enum('urgency', ['low', 'medium', 'high', 'critical'])->default('medium');

        // Assignment and handling
        $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
        $table->timestamp('assigned_at')->nullable();

        // ISO 21001 Compliance fields
        $table->text('impact_assessment')->nullable(); // Impact on educational quality
        $table->text('root_cause_analysis')->nullable(); // Root cause analysis
        $table->text('corrective_actions')->nullable(); // Actions taken
        $table->text('preventive_actions')->nullable(); // Prevention measures
        $table->timestamp('target_resolution_date')->nullable();
        $table->timestamp('actual_resolution_date')->nullable();

        // Satisfaction and follow-up
        $table->integer('satisfaction_rating')->nullable(); // 1-5 rating
        $table->text('satisfaction_comments')->nullable();
        $table->boolean('requires_follow_up')->default(false);
        $table->timestamp('follow_up_date')->nullable();

        // Audit trail
        $table->json('audit_trail')->nullable(); // JSON log of all changes
        $table->timestamps();

        // Indexes for performance
        $table->index(['type', 'feedback_status_id']);
        $table->index(['feedback_category_id', 'created_at']);
        $table->index(['assigned_to', 'feedback_status_id']);
        $table->index('tracking_number');
    });
}

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feedback');
    }
};
