<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
public function up()
{
    Schema::create('feedback', function (Blueprint $table) {
        $table->id();
        $table->unsignedBigInteger('complaint_id');
        $table->unsignedBigInteger('user_id'); // The teacher/admin giving feedback
        $table->text('response');
        $table->timestamps();

        $table->foreign('complaint_id')->references('id')->on('complaints')->onDelete('cascade');
        $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
    });
}

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feedback');
    }
};
