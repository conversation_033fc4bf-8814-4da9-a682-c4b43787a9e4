<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Feedback;
use App\Models\FeedbackCategory;
use App\Models\FeedbackStatus;
use App\Models\FeedbackResponse;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FeedbackController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!Auth::user() || !Auth::user()->canManageFeedback()) {
                abort(403, 'Unauthorized access to admin panel.');
            }
            return $next($request);
        });
    }

    public function index(Request $request)
    {
        $query = Feedback::with(['user', 'category', 'status', 'assignedUser']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('feedback_status_id', $request->status);
        }

        if ($request->filled('category')) {
            $query->where('feedback_category_id', $request->category);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('assigned_to')) {
            if ($request->assigned_to === 'unassigned') {
                $query->whereNull('assigned_to');
            } else {
                $query->where('assigned_to', $request->assigned_to);
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('tracking_number', 'like', "%{$search}%");
            });
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $feedback = $query->paginate(20)->withQueryString();

        // Get filter options
        $categories = FeedbackCategory::active()->ordered()->get();
        $statuses = FeedbackStatus::active()->ordered()->get();
        $users = User::where('role', '!=', 'student')->orderBy('name')->get();

        return view('admin.feedback.index', compact(
            'feedback', 'categories', 'statuses', 'users'
        ));
    }

    public function show(Feedback $feedback)
    {
        $feedback->load([
            'user', 'category', 'status', 'assignedUser',
            'responses.user', 'attachments.uploadedBy'
        ]);

        $categories = FeedbackCategory::active()->ordered()->get();
        $statuses = FeedbackStatus::active()->ordered()->get();
        $users = User::where('role', '!=', 'student')->orderBy('name')->get();

        return view('admin.feedback.show', compact(
            'feedback', 'categories', 'statuses', 'users'
        ));
    }

    public function update(Request $request, Feedback $feedback)
    {
        $request->validate([
            'feedback_status_id' => 'required|exists:feedback_statuses,id',
            'assigned_to' => 'nullable|exists:users,id',
            'priority' => 'required|in:low,medium,high,urgent',
            'urgency' => 'required|in:low,medium,high,critical',
            'target_resolution_date' => 'nullable|date',
            'impact_assessment' => 'nullable|string',
            'root_cause_analysis' => 'nullable|string',
            'corrective_actions' => 'nullable|string',
            'preventive_actions' => 'nullable|string',
        ]);

        $oldStatus = $feedback->status;
        $oldAssignee = $feedback->assignedUser;

        $feedback->update($request->validated());

        // Log status change
        if ($oldStatus->id !== $feedback->feedback_status_id) {
            $newStatus = FeedbackStatus::find($feedback->feedback_status_id);
            $feedback->addToAuditTrail('status_changed', [
                'old_status' => $oldStatus->name,
                'new_status' => $newStatus->name,
                'changed_by' => Auth::user()->name,
            ]);

            // Create response for status change
            FeedbackResponse::create([
                'feedback_id' => $feedback->id,
                'user_id' => Auth::id(),
                'response' => "Status changed from {$oldStatus->name} to {$newStatus->name}",
                'type' => 'status_change',
                'is_visible_to_submitter' => true,
                'metadata' => [
                    'old_status' => $oldStatus->name,
                    'new_status' => $newStatus->name,
                ],
            ]);
        }

        // Log assignment change
        if ($oldAssignee?->id !== $feedback->assigned_to) {
            $newAssignee = $feedback->assignedUser;
            $feedback->addToAuditTrail('assignment_changed', [
                'old_assignee' => $oldAssignee?->name ?? 'Unassigned',
                'new_assignee' => $newAssignee?->name ?? 'Unassigned',
                'changed_by' => Auth::user()->name,
            ]);

            if ($newAssignee) {
                FeedbackResponse::create([
                    'feedback_id' => $feedback->id,
                    'user_id' => Auth::id(),
                    'response' => "Assigned to {$newAssignee->name}",
                    'type' => 'assignment',
                    'is_visible_to_submitter' => false,
                    'metadata' => [
                        'assigned_to' => $newAssignee->name,
                    ],
                ]);
            }
        }

        return redirect()->back()->with('success', 'Feedback updated successfully.');
    }

    public function addResponse(Request $request, Feedback $feedback)
    {
        $request->validate([
            'response' => 'required|string',
            'type' => 'required|in:internal_note,public_response',
            'is_visible_to_submitter' => 'boolean',
        ]);

        $response = FeedbackResponse::create([
            'feedback_id' => $feedback->id,
            'user_id' => Auth::id(),
            'response' => $request->response,
            'type' => $request->type,
            'is_visible_to_submitter' => $request->boolean('is_visible_to_submitter', $request->type === 'public_response'),
        ]);

        $feedback->addToAuditTrail('response_added', [
            'response_type' => $response->type,
            'is_public' => $response->is_visible_to_submitter,
            'added_by' => Auth::user()->name,
        ]);

        return redirect()->back()->with('success', 'Response added successfully.');
    }

    public function dashboard()
    {
        $stats = [
            'total' => Feedback::count(),
            'pending' => Feedback::whereHas('status', fn($q) => $q->where('slug', 'submitted'))->count(),
            'in_progress' => Feedback::whereHas('status', fn($q) => $q->where('slug', 'in-progress'))->count(),
            'resolved' => Feedback::whereHas('status', fn($q) => $q->where('is_final', true))->count(),
        ];

        $recentFeedback = Feedback::with(['user', 'category', 'status'])
            ->latest()
            ->limit(10)
            ->get();

        $categoryStats = FeedbackCategory::withCount('feedback')
            ->orderBy('feedback_count', 'desc')
            ->limit(10)
            ->get();

        return view('admin.dashboard', compact('stats', 'recentFeedback', 'categoryStats'));
    }
}
