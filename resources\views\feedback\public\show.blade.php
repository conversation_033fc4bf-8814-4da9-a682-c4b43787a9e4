<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Details - {{ $feedback->tracking_number }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ $feedback->title }}</h1>
                    <p class="text-gray-600">Tracking Number: <span class="font-mono font-semibold">{{ $feedback->tracking_number }}</span></p>
                </div>
                <div class="text-right">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium" 
                          style="background-color: {{ $feedback->status->color }}20; color: {{ $feedback->status->color }};">
                        {{ $feedback->status->name }}
                    </span>
                </div>
            </div>

            <!-- Feedback Details -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Type</h3>
                        <p class="mt-1 text-lg font-semibold text-gray-900 capitalize">
                            <i class="fas {{ $feedback->type === 'feedback' ? 'fa-comment-dots' : ($feedback->type === 'suggestion' ? 'fa-lightbulb' : 'fa-exclamation-triangle') }} mr-2"></i>
                            {{ $feedback->type }}
                        </p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Category</h3>
                        <p class="mt-1 text-lg font-semibold text-gray-900">{{ $feedback->category->name }}</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Priority</h3>
                        <p class="mt-1 text-lg font-semibold text-gray-900 capitalize">
                            <span class="inline-flex items-center px-2 py-1 rounded text-sm font-medium
                                {{ $feedback->priority === 'urgent' ? 'bg-red-100 text-red-800' : 
                                   ($feedback->priority === 'high' ? 'bg-orange-100 text-orange-800' : 
                                   ($feedback->priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800')) }}">
                                {{ $feedback->priority }}
                            </span>
                        </p>
                    </div>
                </div>

                <div class="border-t pt-6">
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Description</h3>
                    <div class="prose max-w-none">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ $feedback->description }}</p>
                    </div>
                </div>

                <div class="border-t pt-6 mt-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Submitted By</h3>
                            <p class="mt-1 text-gray-900">{{ $feedback->submitter_name }}</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Submitted On</h3>
                            <p class="mt-1 text-gray-900">{{ $feedback->created_at->format('F j, Y \a\t g:i A') }}</p>
                        </div>
                    </div>
                </div>

                @if($feedback->attachments->count() > 0)
                <div class="border-t pt-6 mt-6">
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-4">Attachments</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach($feedback->attachments as $attachment)
                        <div class="flex items-center p-3 border border-gray-200 rounded-lg">
                            <div class="flex-shrink-0 mr-3">
                                @if($attachment->isImage())
                                    <i class="fas fa-image text-blue-500 text-xl"></i>
                                @elseif($attachment->isPdf())
                                    <i class="fas fa-file-pdf text-red-500 text-xl"></i>
                                @else
                                    <i class="fas fa-file text-gray-500 text-xl"></i>
                                @endif
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">{{ $attachment->original_name }}</p>
                                <p class="text-sm text-gray-500">{{ $attachment->file_size_human }}</p>
                            </div>
                            <div class="flex-shrink-0">
                                <a href="{{ $attachment->download_url }}" 
                                   class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>

            <!-- Timeline/Responses -->
            @if($feedback->responses->count() > 0)
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">
                    <i class="fas fa-comments mr-2"></i>
                    Updates & Responses
                </h2>
                
                <div class="space-y-6">
                    @foreach($feedback->responses as $response)
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-blue-600"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">{{ $response->user->name }}</h4>
                                <span class="text-sm text-gray-500">{{ $response->created_at->format('M j, Y \a\t g:i A') }}</span>
                            </div>
                            <div class="prose max-w-none">
                                <p class="text-gray-700 whitespace-pre-wrap">{{ $response->response }}</p>
                            </div>
                            @if($response->type === 'status_change' && isset($response->metadata['old_status'], $response->metadata['new_status']))
                            <div class="mt-2 p-2 bg-gray-50 rounded text-sm">
                                <i class="fas fa-exchange-alt mr-1"></i>
                                Status changed from <strong>{{ $response->metadata['old_status'] }}</strong> to <strong>{{ $response->metadata['new_status'] }}</strong>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Progress Timeline -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">
                    <i class="fas fa-timeline mr-2"></i>
                    Progress Timeline
                </h2>
                
                <div class="relative">
                    <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                    
                    <div class="relative flex items-center mb-4">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">Submitted</h4>
                            <p class="text-sm text-gray-500">{{ $feedback->created_at->format('M j, Y \a\t g:i A') }}</p>
                        </div>
                    </div>

                    @if($feedback->status->slug !== 'submitted')
                    <div class="relative flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-eye text-white text-sm"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">{{ $feedback->status->name }}</h4>
                            <p class="text-sm text-gray-500">Current status</p>
                        </div>
                    </div>
                    @endif

                    @if($feedback->target_resolution_date)
                    <div class="relative flex items-center">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-calendar text-gray-600 text-sm"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">Target Resolution</h4>
                            <p class="text-sm text-gray-500">{{ $feedback->target_resolution_date->format('M j, Y') }}</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Navigation -->
            <div class="flex justify-between items-center">
                <a href="{{ route('feedback.track') }}" 
                   class="inline-flex items-center text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Track Another Feedback
                </a>
                <a href="{{ route('feedback.create') }}" 
                   class="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    <i class="fas fa-plus mr-2"></i>
                    Submit New Feedback
                </a>
            </div>
        </div>
    </div>
</body>
</html>
