<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('feedback_responses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('feedback_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Responder
            $table->text('response');
            $table->enum('type', ['internal_note', 'public_response', 'status_change', 'assignment'])->default('public_response');
            $table->boolean('is_visible_to_submitter')->default(true);
            $table->json('metadata')->nullable(); // Additional data like old/new status, etc.
            $table->timestamps();
            
            $table->index(['feedback_id', 'created_at']);
            $table->index(['user_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feedback_responses');
    }
};
