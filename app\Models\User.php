<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Complaint;
use App\Models\Feedback;
use App\Models\FeedbackResponse;
use App\Models\FeedbackAttachment;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'student_id',
        'employee_id',
        'department',
        'is_active',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    // Relationships
    public function complaints(): HasMany
    {
        return $this->hasMany(Complaint::class);
    }

    public function submittedFeedback(): HasMany
    {
        return $this->hasMany(Feedback::class, 'user_id');
    }

    public function assignedFeedback(): HasMany
    {
        return $this->hasMany(Feedback::class, 'assigned_to');
    }

    public function feedbackResponses(): HasMany
    {
        return $this->hasMany(FeedbackResponse::class);
    }

    public function uploadedAttachments(): HasMany
    {
        return $this->hasMany(FeedbackAttachment::class, 'uploaded_by');
    }

    // Helper methods
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    public function isTeacher(): bool
    {
        return $this->role === 'teacher';
    }

    public function isStudent(): bool
    {
        return $this->role === 'student';
    }

    public function canManageFeedback(): bool
    {
        return in_array($this->role, ['admin', 'teacher']);
    }

    public function getDisplayIdAttribute(): string
    {
        return $this->student_id ?? $this->employee_id ?? 'N/A';
    }
}
