<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class FeedbackCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Academic Programs',
                'slug' => 'academic-programs',
                'description' => 'Feedback about curriculum, courses, teaching methods, and academic quality',
                'color' => '#3B82F6',
                'icon' => 'heroicon-o-academic-cap',
                'sort_order' => 1,
            ],
            [
                'name' => 'Teaching Quality',
                'slug' => 'teaching-quality',
                'description' => 'Feedback about instructor performance, teaching effectiveness, and classroom experience',
                'color' => '#10B981',
                'icon' => 'heroicon-o-user-group',
                'sort_order' => 2,
            ],
            [
                'name' => 'Facilities & Infrastructure',
                'slug' => 'facilities-infrastructure',
                'description' => 'Issues with buildings, classrooms, laboratories, libraries, and equipment',
                'color' => '#F59E0B',
                'icon' => 'heroicon-o-building-office',
                'sort_order' => 3,
            ],
            [
                'name' => 'Student Services',
                'slug' => 'student-services',
                'description' => 'Registration, counseling, career services, student support, and administrative services',
                'color' => '#8B5CF6',
                'icon' => 'heroicon-o-users',
                'sort_order' => 4,
            ],
            [
                'name' => 'Technology & IT',
                'slug' => 'technology-it',
                'description' => 'Learning management systems, Wi-Fi, computers, software, and technical support',
                'color' => '#06B6D4',
                'icon' => 'heroicon-o-computer-desktop',
                'sort_order' => 5,
            ],
            [
                'name' => 'Library Services',
                'slug' => 'library-services',
                'description' => 'Library resources, access, staff, and study spaces',
                'color' => '#84CC16',
                'icon' => 'heroicon-o-book-open',
                'sort_order' => 6,
            ],
            [
                'name' => 'Food Services',
                'slug' => 'food-services',
                'description' => 'Cafeteria, dining options, food quality, and meal plans',
                'color' => '#EF4444',
                'icon' => 'heroicon-o-cake',
                'sort_order' => 7,
            ],
            [
                'name' => 'Transportation',
                'slug' => 'transportation',
                'description' => 'Campus shuttle, parking, public transport access',
                'color' => '#6366F1',
                'icon' => 'heroicon-o-truck',
                'sort_order' => 8,
            ],
            [
                'name' => 'Safety & Security',
                'slug' => 'safety-security',
                'description' => 'Campus safety, security measures, emergency procedures',
                'color' => '#DC2626',
                'icon' => 'heroicon-o-shield-check',
                'sort_order' => 9,
            ],
            [
                'name' => 'Administration',
                'slug' => 'administration',
                'description' => 'Administrative processes, policies, communication, and management',
                'color' => '#6B7280',
                'icon' => 'heroicon-o-building-office-2',
                'sort_order' => 10,
            ],
            [
                'name' => 'Other',
                'slug' => 'other',
                'description' => 'General feedback and suggestions not covered by other categories',
                'color' => '#9CA3AF',
                'icon' => 'heroicon-o-ellipsis-horizontal',
                'sort_order' => 99,
            ],
        ];

        foreach ($categories as $category) {
            DB::table('feedback_categories')->insert([
                ...$category,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
