<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Feedback - Educational Institution</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Submit Feedback</h1>
                <p class="text-gray-600">Help us improve our educational services by sharing your feedback, suggestions, or concerns.</p>
            </div>

            <!-- Success Message -->
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    <i class="fas fa-check-circle mr-2"></i>
                    {{ session('success') }}
                </div>
            @endif

            <!-- Error Messages -->
            @if($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <ul class="list-disc list-inside">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- Feedback Form -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <form action="{{ route('feedback.store') }}" method="POST" enctype="multipart/form-data" id="feedbackForm">
                    @csrf

                    <!-- Type Selection -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Type of Submission</label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <label class="relative">
                                <input type="radio" name="type" value="feedback" class="sr-only peer" {{ old('type', 'feedback') == 'feedback' ? 'checked' : '' }}>
                                <div class="p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50">
                                    <i class="fas fa-comment-dots text-blue-500 text-xl mb-2"></i>
                                    <h3 class="font-medium">Feedback</h3>
                                    <p class="text-sm text-gray-600">Share your thoughts and experiences</p>
                                </div>
                            </label>
                            <label class="relative">
                                <input type="radio" name="type" value="suggestion" class="sr-only peer" {{ old('type') == 'suggestion' ? 'checked' : '' }}>
                                <div class="p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-green-500 peer-checked:bg-green-50">
                                    <i class="fas fa-lightbulb text-green-500 text-xl mb-2"></i>
                                    <h3 class="font-medium">Suggestion</h3>
                                    <p class="text-sm text-gray-600">Propose improvements or new ideas</p>
                                </div>
                            </label>
                            <label class="relative">
                                <input type="radio" name="type" value="complaint" class="sr-only peer" {{ old('type') == 'complaint' ? 'checked' : '' }}>
                                <div class="p-4 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-red-500 peer-checked:bg-red-50">
                                    <i class="fas fa-exclamation-triangle text-red-500 text-xl mb-2"></i>
                                    <h3 class="font-medium">Complaint</h3>
                                    <p class="text-sm text-gray-600">Report issues or concerns</p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Category -->
                    <div class="mb-6">
                        <label for="feedback_category_id" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="feedback_category_id" id="feedback_category_id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <option value="">Select a category</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ old('feedback_category_id') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Title -->
                    <div class="mb-6">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                        <input type="text" name="title" id="title" value="{{ old('title') }}" 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
                               placeholder="Brief summary of your feedback" required>
                    </div>

                    <!-- Description -->
                    <div class="mb-6">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea name="description" id="description" rows="6" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                  placeholder="Please provide detailed information about your feedback..." required>{{ old('description') }}</textarea>
                    </div>

                    <!-- Priority -->
                    <div class="mb-6">
                        <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                        <select name="priority" id="priority" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <option value="low" {{ old('priority', 'medium') == 'low' ? 'selected' : '' }}>Low</option>
                            <option value="medium" {{ old('priority', 'medium') == 'medium' ? 'selected' : '' }}>Medium</option>
                            <option value="high" {{ old('priority', 'medium') == 'high' ? 'selected' : '' }}>High</option>
                            <option value="urgent" {{ old('priority', 'medium') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                        </select>
                    </div>

                    <!-- File Attachments -->
                    <div class="mb-6">
                        <label for="attachments" class="block text-sm font-medium text-gray-700 mb-2">Attachments (Optional)</label>
                        <input type="file" name="attachments[]" id="attachments" multiple 
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif">
                        <p class="text-sm text-gray-500 mt-1">Maximum 10MB per file. Supported formats: PDF, DOC, DOCX, TXT, JPG, PNG, GIF</p>
                    </div>

                    <!-- Anonymous Submission -->
                    <div class="mb-6">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_anonymous" value="1" class="mr-2" {{ old('is_anonymous') ? 'checked' : '' }} onchange="toggleAnonymousFields()">
                            <span class="text-sm font-medium text-gray-700">Submit anonymously</span>
                        </label>
                    </div>

                    <!-- Anonymous Fields -->
                    <div id="anonymousFields" class="mb-6 {{ old('is_anonymous') ? '' : 'hidden' }}">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="anonymous_name" class="block text-sm font-medium text-gray-700 mb-2">Name (Optional)</label>
                                <input type="text" name="anonymous_name" id="anonymous_name" value="{{ old('anonymous_name') }}" 
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                       placeholder="Your name or leave blank for complete anonymity">
                            </div>
                            <div>
                                <label for="anonymous_email" class="block text-sm font-medium text-gray-700 mb-2">Email (Optional)</label>
                                <input type="email" name="anonymous_email" id="anonymous_email" value="{{ old('anonymous_email') }}" 
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                       placeholder="For updates on your feedback">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-between items-center">
                        <a href="{{ route('feedback.track') }}" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-search mr-1"></i>
                            Track existing feedback
                        </a>
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Submit Feedback
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function toggleAnonymousFields() {
            const checkbox = document.querySelector('input[name="is_anonymous"]');
            const fields = document.getElementById('anonymousFields');
            
            if (checkbox.checked) {
                fields.classList.remove('hidden');
            } else {
                fields.classList.add('hidden');
            }
        }
    </script>
</body>
</html>
