<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ComplaintController;
use App\Http\Controllers\FeedbackController;
use App\Http\Controllers\PublicFeedbackController;
use Illuminate\Support\Facades\Auth;

// Public routes
Route::get('/', function () {
    return redirect()->route('feedback.create');
});

// Authentication routes
Route::get('/login', function () {
    return view('auth.login');
})->name('login');

Route::post('/login', function (\Illuminate\Http\Request $request) {
    $credentials = $request->validate([
        'email' => 'required|email',
        'password' => 'required',
    ]);

    if (Auth::attempt($credentials)) {
        $request->session()->regenerate();

        if (Auth::user()->canManageFeedback()) {
            return redirect()->route('admin.dashboard');
        }

        return redirect()->route('feedback.create');
    }

    return back()->withErrors([
        'email' => 'The provided credentials do not match our records.',
    ]);
})->name('login.post');

Route::post('/logout', function (\Illuminate\Http\Request $request) {
    Auth::logout();
    $request->session()->invalidate();
    $request->session()->regenerateToken();
    return redirect()->route('feedback.create');
})->name('logout');

// Public feedback routes
Route::prefix('feedback')->name('feedback.')->group(function () {
    Route::get('/', [PublicFeedbackController::class, 'index'])->name('create');
    Route::post('/', [PublicFeedbackController::class, 'store'])->name('store');
    Route::get('/track/{trackingNumber?}', [PublicFeedbackController::class, 'track'])->name('track');
    Route::post('/track', [PublicFeedbackController::class, 'trackPost'])->name('track.post');
});

// Authenticated routes
Route::middleware(['auth'])->group(function () {
    Route::resource('complaints', ComplaintController::class);
    Route::resource('feedbacks', FeedbackController::class);

    // Admin routes
    Route::prefix('admin')->name('admin.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\FeedbackController::class, 'dashboard'])->name('dashboard');
        Route::get('/feedback', [App\Http\Controllers\Admin\FeedbackController::class, 'index'])->name('feedback.index');
        Route::get('/feedback/{feedback}', [App\Http\Controllers\Admin\FeedbackController::class, 'show'])->name('feedback.show');
        Route::put('/feedback/{feedback}', [App\Http\Controllers\Admin\FeedbackController::class, 'update'])->name('feedback.update');
        Route::post('/feedback/{feedback}/response', [App\Http\Controllers\Admin\FeedbackController::class, 'addResponse'])->name('feedback.response');

        // Placeholder routes for future features
        Route::get('/reports', function () { return view('admin.reports'); })->name('reports');
        Route::get('/settings', function () { return view('admin.settings'); })->name('settings');
    });
});
