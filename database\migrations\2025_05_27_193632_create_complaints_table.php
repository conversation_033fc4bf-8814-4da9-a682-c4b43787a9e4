<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
   public function up(): void
{
    // This table is now deprecated in favor of the feedback table
    // We'll keep it for backward compatibility but redirect to feedback
    Schema::create('complaints', function (Blueprint $table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->string('subject');
        $table->text('description');
        $table->enum('status', ['pending', 'in_progress', 'resolved'])->default('pending');
        $table->foreignId('feedback_id')->nullable()->constrained()->onDelete('set null'); // Link to new feedback system
        $table->boolean('migrated_to_feedback')->default(false);
        $table->timestamps();
    });
}


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('complaints');
    }
};
