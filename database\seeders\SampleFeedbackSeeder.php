<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Feedback;
use App\Models\FeedbackCategory;
use App\Models\FeedbackStatus;
use App\Models\User;

class SampleFeedbackSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = FeedbackCategory::all();
        $statuses = FeedbackStatus::all();
        $users = User::all();

        if ($categories->isEmpty() || $statuses->isEmpty()) {
            $this->command->warn('Please run FeedbackCategorySeeder and FeedbackStatusSeeder first.');
            return;
        }

        $sampleFeedback = [
            [
                'type' => 'complaint',
                'title' => 'Wi-Fi connectivity issues in library',
                'description' => 'The Wi-Fi connection in the main library is very slow and frequently disconnects. This makes it difficult to complete online research and assignments. The issue has been ongoing for the past two weeks.',
                'priority' => 'high',
                'category_slug' => 'technology-it',
                'is_anonymous' => false,
            ],
            [
                'type' => 'suggestion',
                'title' => 'Extended library hours during exam period',
                'description' => 'I would like to suggest extending the library hours during the exam period. Currently, the library closes at 10 PM, but many students would benefit from having access until midnight or even 24/7 during finals week.',
                'priority' => 'medium',
                'category_slug' => 'library-services',
                'is_anonymous' => false,
            ],
            [
                'type' => 'feedback',
                'title' => 'Excellent teaching quality in Computer Science department',
                'description' => 'I want to commend the Computer Science department for their excellent teaching quality. The professors are knowledgeable, approachable, and always willing to help students understand complex concepts.',
                'priority' => 'low',
                'category_slug' => 'teaching-quality',
                'is_anonymous' => false,
            ],
            [
                'type' => 'complaint',
                'title' => 'Cafeteria food quality concerns',
                'description' => 'The food quality in the main cafeteria has declined significantly. Several students have reported stomach issues after eating there. Please investigate the food preparation and storage procedures.',
                'priority' => 'urgent',
                'category_slug' => 'food-services',
                'is_anonymous' => true,
                'anonymous_name' => 'Concerned Student',
                'anonymous_email' => '<EMAIL>',
            ],
            [
                'type' => 'suggestion',
                'title' => 'More parking spaces needed',
                'description' => 'The campus parking situation is becoming increasingly difficult. With the growing number of students, we need more parking spaces or alternative transportation solutions like a shuttle service.',
                'priority' => 'medium',
                'category_slug' => 'transportation',
                'is_anonymous' => false,
            ],
            [
                'type' => 'complaint',
                'title' => 'Broken air conditioning in Building A',
                'description' => 'The air conditioning system in Building A has been broken for over a week. The classrooms are uncomfortably hot, making it difficult to concentrate during lectures.',
                'priority' => 'high',
                'category_slug' => 'facilities-infrastructure',
                'is_anonymous' => false,
            ],
        ];

        foreach ($sampleFeedback as $feedbackData) {
            $category = $categories->where('slug', $feedbackData['category_slug'])->first();
            $initialStatus = $statuses->where('is_initial', true)->first();
            
            if (!$category || !$initialStatus) {
                continue;
            }

            $user = null;
            if (!$feedbackData['is_anonymous']) {
                $user = $users->where('role', 'student')->random();
            }

            $feedback = Feedback::create([
                'type' => $feedbackData['type'],
                'title' => $feedbackData['title'],
                'description' => $feedbackData['description'],
                'user_id' => $user?->id,
                'anonymous_name' => $feedbackData['anonymous_name'] ?? null,
                'anonymous_email' => $feedbackData['anonymous_email'] ?? null,
                'is_anonymous' => $feedbackData['is_anonymous'],
                'feedback_category_id' => $category->id,
                'feedback_status_id' => $initialStatus->id,
                'priority' => $feedbackData['priority'],
                'urgency' => $feedbackData['priority'], // Match urgency to priority for simplicity
            ]);

            // Add some feedback to different statuses
            if (rand(1, 3) === 1) {
                $randomStatus = $statuses->where('is_initial', false)->random();
                $feedback->update(['feedback_status_id' => $randomStatus->id]);
                
                // Assign some feedback to staff
                if (rand(1, 2) === 1) {
                    $staff = $users->whereIn('role', ['teacher', 'admin'])->random();
                    $feedback->update(['assigned_to' => $staff->id, 'assigned_at' => now()]);
                }
            }

            $feedback->addToAuditTrail('created', [
                'type' => $feedback->type,
                'category' => $category->name,
                'submitter' => $feedback->is_anonymous ? 'Anonymous' : $user->name,
            ]);
        }

        $this->command->info('Sample feedback created successfully!');
    }
}
