<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Dashboard - Feedback System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feedback-list {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feedback-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            font-size: 1.2em;
            font-weight: bold;
        }
        .feedback-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .feedback-item:last-child {
            border-bottom: none;
        }
        .feedback-info h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .feedback-meta {
            color: #666;
            font-size: 0.9em;
        }
        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-in_progress { background: #cce5ff; color: #004085; }
        .status-resolved { background: #d4edda; color: #155724; }
        .status-closed { background: #f8d7da; color: #721c24; }
        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        .nav-links {
            display: flex;
            gap: 15px;
        }
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 4px;
            background: #e7f3ff;
        }
        .nav-links a:hover {
            background: #cce5ff;
        }
    </style>
</head>
<body>
    <div class="header">
        <div>
            <h1>Teacher Dashboard</h1>
            <p>Welcome, {{ auth()->user()->name }}</p>
        </div>
        <div>
            <div class="nav-links">
                <a href="{{ route('feedback.submit') }}">Submit Feedback</a>
                <a href="{{ route('feedback.track') }}">Track Feedback</a>
            </div>
            <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                @csrf
                <button type="submit" class="logout-btn">Logout</button>
            </form>
        </div>
    </div>

    <div class="feedback-list">
        <div class="feedback-header">All Feedback (View Only)</div>
        @forelse($feedback as $item)
            <div class="feedback-item">
                <div class="feedback-info">
                    <h4>{{ $item->title }}</h4>
                    <div class="feedback-meta">
                        By: {{ $item->user->name }} | 
                        Tracking: {{ $item->tracking_number }} | 
                        {{ $item->created_at->format('M j, Y g:i A') }}
                    </div>
                    <div style="margin-top: 10px; color: #555;">
                        {{ substr($item->description, 0, 100) }}{{ strlen($item->description) > 100 ? '...' : '' }}
                    </div>
                </div>
                <div>
                    <span class="status status-{{ $item->status }}">{{ ucfirst(str_replace('_', ' ', $item->status)) }}</span>
                </div>
            </div>
        @empty
            <div class="feedback-item">
                <div style="text-align: center; color: #666;">No feedback submitted yet.</div>
            </div>
        @endforelse
    </div>
</body>
</html>
