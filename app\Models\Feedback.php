<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Feedback extends Model
{
    use HasFactory;

    protected $fillable = [
        'tracking_number',
        'type',
        'title',
        'description',
        'user_id',
        'anonymous_name',
        'anonymous_email',
        'is_anonymous',
        'feedback_category_id',
        'feedback_status_id',
        'priority',
        'urgency',
        'assigned_to',
        'assigned_at',
        'impact_assessment',
        'root_cause_analysis',
        'corrective_actions',
        'preventive_actions',
        'target_resolution_date',
        'actual_resolution_date',
        'satisfaction_rating',
        'satisfaction_comments',
        'requires_follow_up',
        'follow_up_date',
        'audit_trail',
    ];

    protected $casts = [
        'is_anonymous' => 'boolean',
        'assigned_at' => 'datetime',
        'target_resolution_date' => 'datetime',
        'actual_resolution_date' => 'datetime',
        'requires_follow_up' => 'boolean',
        'follow_up_date' => 'datetime',
        'audit_trail' => 'array',
        'satisfaction_rating' => 'integer',
    ];

    // Boot method to generate tracking number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($feedback) {
            if (empty($feedback->tracking_number)) {
                $feedback->tracking_number = static::generateTrackingNumber();
            }
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(FeedbackCategory::class, 'feedback_category_id');
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(FeedbackStatus::class, 'feedback_status_id');
    }

    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function responses(): HasMany
    {
        return $this->hasMany(FeedbackResponse::class);
    }

    public function attachments(): HasMany
    {
        return $this->hasMany(FeedbackAttachment::class);
    }

    // Helper methods
    public static function generateTrackingNumber(): string
    {
        do {
            $number = 'FB' . date('Y') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
        } while (static::where('tracking_number', $number)->exists());

        return $number;
    }

    public function getSubmitterNameAttribute(): string
    {
        return $this->is_anonymous ? ($this->anonymous_name ?? 'Anonymous') : $this->user->name;
    }

    public function getSubmitterEmailAttribute(): ?string
    {
        return $this->is_anonymous ? $this->anonymous_email : $this->user->email;
    }

    public function addToAuditTrail(string $action, array $data = []): void
    {
        $trail = $this->audit_trail ?? [];
        $trail[] = [
            'action' => $action,
            'data' => $data,
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
        ];
        $this->update(['audit_trail' => $trail]);
    }
}
