<?php

namespace App\Http\Controllers;

use App\Models\Feedback;
use App\Models\FeedbackCategory;
use App\Models\FeedbackStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class PublicFeedbackController extends Controller
{
    public function index()
    {
        $categories = FeedbackCategory::active()->ordered()->get();
        return view('feedback.public.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:feedback,suggestion,complaint',
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:5000',
            'feedback_category_id' => 'required|exists:feedback_categories,id',
            'priority' => 'required|in:low,medium,high,urgent',
            'is_anonymous' => 'boolean',
            'anonymous_name' => 'required_if:is_anonymous,true|string|max:255',
            'anonymous_email' => 'required_if:is_anonymous,true|email|max:255',
            'attachments.*' => 'file|max:10240|mimes:pdf,doc,docx,txt,jpg,jpeg,png,gif', // 10MB max
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->validated();
        
        // Set initial status
        $data['feedback_status_id'] = FeedbackStatus::getInitialStatus()?->id ?? 1;
        
        // Set user if authenticated and not anonymous
        if (auth()->check() && !$request->boolean('is_anonymous')) {
            $data['user_id'] = auth()->id();
            $data['is_anonymous'] = false;
            unset($data['anonymous_name'], $data['anonymous_email']);
        } else {
            $data['is_anonymous'] = true;
            $data['user_id'] = null;
        }

        // Set default urgency if not provided
        $data['urgency'] = $data['priority']; // Match urgency to priority for now

        $feedback = Feedback::create($data);

        // Handle file uploads
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $this->storeAttachment($feedback, $file);
            }
        }

        // Add to audit trail
        $feedback->addToAuditTrail('created', [
            'type' => $feedback->type,
            'category' => $feedback->category->name,
            'submitter' => $feedback->is_anonymous ? 'Anonymous' : $feedback->user->name,
        ]);

        return redirect()->route('feedback.track', $feedback->tracking_number)
            ->with('success', 'Your feedback has been submitted successfully! Your tracking number is: ' . $feedback->tracking_number);
    }

    public function track($trackingNumber = null)
    {
        if (!$trackingNumber) {
            return view('feedback.public.track');
        }

        $feedback = Feedback::where('tracking_number', $trackingNumber)->first();

        if (!$feedback) {
            return view('feedback.public.track')->withErrors(['tracking_number' => 'Feedback not found.']);
        }

        // Load relationships
        $feedback->load(['category', 'status', 'responses' => function ($query) {
            $query->public()->orderBy('created_at');
        }, 'attachments' => function ($query) {
            $query->where('is_public', true);
        }]);

        return view('feedback.public.show', compact('feedback'));
    }

    public function trackPost(Request $request)
    {
        $request->validate([
            'tracking_number' => 'required|string',
        ]);

        return redirect()->route('feedback.track', $request->tracking_number);
    }

    private function storeAttachment(Feedback $feedback, $file)
    {
        $originalName = $file->getClientOriginalName();
        $fileName = time() . '_' . $originalName;
        $path = $file->storeAs('feedback-attachments', $fileName, 'private');

        $feedback->attachments()->create([
            'uploaded_by' => auth()->id() ?? null,
            'original_name' => $originalName,
            'file_name' => $fileName,
            'file_path' => $path,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'file_hash' => hash_file('sha256', $file->getRealPath()),
            'is_public' => true, // Public submissions are visible to submitter
            'description' => null,
        ]);
    }
}
