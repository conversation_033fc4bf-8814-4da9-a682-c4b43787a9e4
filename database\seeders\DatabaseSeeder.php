<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed feedback categories and statuses first
        $this->call([
            FeedbackCategorySeeder::class,
            FeedbackStatusSeeder::class,
        ]);

        // Create admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'employee_id' => 'ADM001',
            'department' => 'Administration',
        ]);

        // Create teacher user
        User::factory()->create([
            'name' => 'Teacher User',
            'email' => '<EMAIL>',
            'role' => 'teacher',
            'employee_id' => 'TCH001',
            'department' => 'Computer Science',
        ]);

        // Create student user
        User::factory()->create([
            'name' => 'Student User',
            'email' => '<EMAIL>',
            'role' => 'student',
            'student_id' => 'STU001',
            'department' => 'Computer Science',
        ]);

        // Create additional test users
        User::factory(10)->create();

        // Create sample feedback
        $this->call([
            SampleFeedbackSeeder::class,
        ]);
    }
}
